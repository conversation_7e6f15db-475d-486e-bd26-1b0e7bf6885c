import {
    Sheet,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ead<PERSON>,
    She<PERSON><PERSON>itle,
} from "@/components/ui/sheet"

import Link from "next/link"

import { ScrollArea } from "@/components/ui/scroll-area";


interface NavbarItem {
    href: string;
    children: React.ReactNode;
}

interface Props {
    items: NavbarItem[];
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export const NavbarSidebar = ({items, open, onOpenChange}: Props) => {
    return(
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent
                side="left"
                className="p-0 transition-none">
                    <SheetHeader className="p-4 border-b">
                        <SheetTitle>
                            Menu
                        </SheetTitle>
                    </SheetHeader>
                    <ScrollArea className="flex flex-col overflow-y-auto h-full pb-2">
                        {items.map((item) => (
                            <Link
                                key={item.href}
                                href={item.href}
                                className="w-full text-left p-4 hover:bg-black hover:text-white flex items-center text-base font-medium"
                                onClick={() => onOpenChange(false)}
                            >
                                {item.children}
                            </Link>
                        ))}

                        <div className="border-t">
                            <Link
                                key="/login"
                                href="/login"
                                className="w-full text-left p-4 hover:bg-pink-400 hover:text-white flex items-center text-base font-medium"
                                onClick={() => onOpenChange(false)}
                            >
                                Login
                            </Link>
                            <Link
                                key="/startselling"
                                href="/startselling"
                                className="w-full text-left p-4 hover:bg-pink-400 hover:text-white flex items-center text-base font-medium"
                                onClick={() => onOpenChange(false)}
                            >
                                Start Selling
                            </Link>
                        </div>

                    </ScrollArea>
            </SheetContent>
        </Sheet>
    )
}